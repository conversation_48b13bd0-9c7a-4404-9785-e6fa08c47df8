<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>[根据主题生成的标题]</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/5.2.1/reveal.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/5.2.1/theme/white.min.css">
    <style>
        /*
         * ===================================================================
         * LLM-Friendly Reveal.js CSS Template by Gemini
         * Version: 1.0
         * Description: A clean, modern, and reusable template for generating
         * professional slides with Reveal.js.
         * ===================================================================
        */

        /*
         * -------------------------------------------------------------------
         * [1] 全局变量与基础样式 (Global Variables & Base Styles)
         * -------------------------------------------------------------------
         * 定义了整个幻灯片的主题颜色、字体和基础元素样式。
         * LLM 可通过修改 --primary-color 等变量来快速更换主题风格。
        */
        :root {
            --primary-color: #005A9C; /* 主色调 (深蓝) - 用于标题和强调元素 */
            --secondary-color: #00A9E0; /* 辅助色 (亮蓝) - 用于链接、图标和次级标题 */
            --accent-color: #F5A623; /* 点缀色 (橙色) - 用于特殊高亮 */
            --text-color: #333333; /* 正文颜色 */
            --bg-color: #FFFFFF; /* 背景色 */
            --bg-light-gray: #f8f9fa; /* 浅灰背景 - 用于卡片、引用块 */
            --border-color: #dee2e6; /* 边框颜色 */
            --base-font-size: 2rem; /* 控制全局字体大小，可按需调整 */
            --border-radius: 8px; /* 圆角大小 */
        }

        .reveal {
            font-family: "Helvetica Neue", "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
            font-size: var(--base-font-size);
            color: var(--text-color);
        }

        .reveal ::selection {
            background: var(--secondary-color);
            color: var(--bg-color);
        }

        /* 标题样式 */
        .reveal h1, .reveal h2, .reveal h3, .reveal h4, .reveal h5, .reveal h6 {
            margin: 0 0 20px 0;
            color: var(--primary-color);
            font-weight: 600;
            text-align: left;
        }

        .reveal h1 { font-size: 2.8em; }
        .reveal h2 { font-size: 2.1em; }
        .reveal h3 { font-size: 1.6em; }
        .reveal p { line-height: 1.6; }

        /* 链接样式 */
        .reveal a {
            color: var(--secondary-color);
            text-decoration: none;
            transition: color 0.2s ease;
        }
        .reveal a:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }
        
        /* 强制所有 section 内容垂直居中 */
        .reveal .slides section {
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 2vw 4vw;
            box-sizing: border-box;
            height: 100%;
        }

        /*
         * -------------------------------------------------------------------
         * [2] 幻灯片结构化样式 (Slide Structure Styles)
         * -------------------------------------------------------------------
         * 为不同功能的幻灯片页面（标题、目录、总结等）提供专用布局。
         * 使用方法: <section class="slide-title">...</section>
        */

        /* 标题页 */
        .slide-title {
            text-align: center;
        }
        .slide-title h1 {
            font-size: 3.5em;
            color: var(--primary-color);
            margin-bottom: 0.25em;
        }
        .slide-title h2.subtitle { /* 副标题 */
            font-size: 1.5em;
            color: var(--secondary-color);
            font-weight: 300;
            margin-top: 0;
            margin-bottom: 1em;
        }
        .slide-title .meta-info { /* 作者/日期等元信息 */
            font-size: 1em;
            color: var(--text-color);
            opacity: 0.8;
        }

        /* 目录页 */
        .slide-toc h2 {
            text-align: center;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 0.5em;
            margin-bottom: 1em;
            width: 80%;
            margin-left: auto;
            margin-right: auto;
        }
        .slide-toc ol {
            list-style: none;
            counter-reset: toc-counter;
            padding-left: 0;
            width: 70%;
            margin: 0 auto;
        }
        .slide-toc ol li {
            counter-increment: toc-counter;
            margin-bottom: 0.8em;
            font-size: 1.2em;
            padding-left: 3em;
            position: relative;
        }
        .slide-toc ol li::before {
            content: counter(toc-counter);
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 2em;
            height: 2em;
            background-color: var(--secondary-color);
            color: var(--bg-color);
            border-radius: 50%;
            text-align: center;
            line-height: 2em;
            font-weight: bold;
        }

        /* 标准内容页 */
        .slide-content > h2:first-child, .slide-content > h3:first-child {
            border-left: 5px solid var(--secondary-color);
            padding-left: 0.75em;
            margin-bottom: 1em;
        }

        /* 总结页 & 结束页 */
        .slide-summary, .slide-end {
            text-align: center;
        }
        .slide-summary ul {
            display: inline-block;
            text-align: left;
            list-style-type: '✔  ';
            padding-left: 2em;
        }
        .slide-summary ul li {
            margin-bottom: 0.5em;
        }
        .slide-end h2 {
            font-size: 3em;
            color: var(--secondary-color);
        }
        .slide-end p {
            font-size: 1.5em;
        }


        /*
         * -------------------------------------------------------------------
         * [3] 可重用内容组件样式 (Reusable Content Component Styles)
         * -------------------------------------------------------------------
         * 定义了卡片、引用、对比布局等常用内容块的样式。
         * 使用方法: <div class="card-list">...</div>
        */

        /* 引用块 */
        .reveal blockquote {
            width: 90%;
            margin: 1em auto;
            padding: 1em 1.5em;
            background: var(--bg-light-gray);
            border-left: 5px solid var(--secondary-color);
            box-shadow: 2px 2px 10px rgba(0,0,0,0.05);
            font-style: italic;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
        }
        .reveal blockquote p { margin: 0; }
        .reveal blockquote footer {
            font-style: normal;
            text-align: right;
            font-size: 0.8em;
            margin-top: 0.5em;
            opacity: 0.8;
        }

        /* 表格 */
        .reveal table {
            width: 100%;
            margin: 1.5em auto;
            border-collapse: collapse;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border-radius: var(--border-radius);
            overflow: hidden; /* 保证圆角效果 */
        }
        .reveal th, .reveal td {
            border: 1px solid var(--border-color);
            padding: 0.6em 0.8em;
            text-align: left;
        }
        .reveal th {
            background-color: var(--primary-color);
            color: var(--bg-color);
            font-weight: bold;
        }
        .reveal tbody tr:nth-child(odd) {
            background-color: var(--bg-light-gray);
        }

        /* 对比布局 (双栏) */
        .comparison-layout {
            display: flex;
            justify-content: space-between;
            gap: 2em;
            width: 100%;
            align-items: flex-start;
        }
        .comparison-layout > div {
            flex: 1;
            padding: 1.5em;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--bg-light-gray);
        }
        .comparison-layout > div h3 {
            margin-top: 0;
            color: var(--secondary-color);
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 0.5em;
        }

        /* 卡片列表布局 */
        .card-list {
            display: grid;
            /* 自动适配列数，最小宽度250px */
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5em;
            width: 100%;
            padding: 1em 0;
        }
        .card {
            background: var(--bg-color);
            border-radius: var(--border-radius);
            padding: 1.5em;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid var(--border-color);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        .card h3 {
            margin-top: 0;
            font-size: 1.2em;
            color: var(--primary-color);
        }
        .card p {
            font-size: 0.9em;
            line-height: 1.5;
        }

        /*
         * -------------------------------------------------------------------
         * [4] 辅助工具类 (Utility Classes)
         * -------------------------------------------------------------------
        */

        /* 高亮文本 */
        .highlight {
            background-color: var(--accent-color);
            color: var(--bg-color);
            padding: 0.2em 0.4em;
            border-radius: 4px;
        }

    </style>
</head>
<body>
    <div class="reveal">
        <div class="slides">
            
            <section class="slide-title">
                <h1>幻灯片主标题</h1>
                <h2 class="subtitle">一个清晰有力的副标题</h2>
                <div class="meta-info">
                    <p>分享人：Gemini / 日期：2025-07-26</p>
                </div>
            </section>
            
            <section class="slide-toc">
                <h2>目录 / Contents</h2>
                <ol>
                    <li>引言：项目背景与目标</li>
                    <li>核心设计理念与原则</li>
                    <li>关键模块功能详解</li>
                    <li>成果展示与数据分析</li>
                    <li>总结与未来展望</li>
                </ol>
            </section>
            
            <section class="slide-content">
                <h2>核心设计理念</h2>
                <ul>
                    <li>用户中心：始终将用户需求放在首位。</li>
                    <li>简洁高效：简化操作流程，提升任务效率。</li>
                    <li>数据驱动：基于数据分析进行迭代优化。</li>
                    <li>安全可靠：保障系统和用户数据的绝对安全。</li>
                </ul>
            </section>

            <section class="slide-content">
                <h2>设计哲学</h2>
                <blockquote>
                    <p>"设计不仅仅是看起来和感觉上的东西。设计是它如何运作。"</p>
                    <footer>— 史蒂夫·乔布斯</footer>
                </blockquote>
            </section>

            <section class="slide-content">
                <h2>三大核心特性</h2>
                <div class="card-list">
                    <div class="card">
                        <h3>智能推荐</h3>
                        <p>利用机器学习算法，为用户提供个性化的内容推荐。</p>
                    </div>
                    <div class="card">
                        <h3>多端同步</h3>
                        <p>支持Web、iOS和Android平台，数据实时同步。</p>
                    </div>
                    <div class="card">
                        <h3>高级安全</h3>
                        <p>采用端到端加密技术，全面保护用户隐私数据安全。</p>
                    </div>
                </div>
            </section>

            <section class="slide-content">
                <h2>方案对比</h2>
                <div class="comparison-layout">
                    <div>
                        <h3>方案 A：传统架构</h3>
                        <ul>
                            <li>开发周期长</li>
                            <li>维护成本高</li>
                            <li>扩展性较差</li>
                        </ul>
                    </div>
                    <div>
                        <h3>方案 B：新架构</h3>
                        <ul>
                            <li><span class="highlight">快速迭代</span></li>
                            <li>易于维护</li>
                            <li>弹性伸缩能力强</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section class="slide-content">
                <h2>季度增长数据</h2>
                <table>
                    <thead>
                        <tr>
                            <th>季度</th>
                            <th>用户增长率</th>
                            <th>收入 (百万)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Q1</td>
                            <td>15%</td>
                            <td>$1.2</td>
                        </tr>
                        <tr>
                            <td>Q2</td>
                            <td>22%</td>
                            <td>$1.8</td>
                        </tr>
                        <tr>
                            <td>Q3</td>
                            <td>18%</td>
                            <td>$2.1</td>
                        </tr>
                    </tbody>
                </table>
            </section>
            
            <section class="slide-summary">
                <h2>总结 / Summary</h2>
                <ul>
                    <li>明确了项目的核心价值与目标</li>
                    <li>展示了新架构的显著优势</li>
                    <li>验证了关键特性的市场潜力</li>
                </ul>
            </section>
            
            <section class="slide-end">
                <h2>感谢观看</h2>
                <p>Q&A / 联系方式：<EMAIL></p>
            </section>

        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/5.2.1/reveal.min.js"></script>
    <script>
        Reveal.initialize({
            hash: true,          // URL中显示幻灯片编号
            transition: 'slide', // 切换效果: none/fade/slide/convex/concave/zoom
            controls: true,      // 显示控制箭头
            progress: true,      // 显示进度条
            center: true,        // 垂直居中幻灯片内容
            // 更多配置项: https://revealjs.com/config/
        });
    </script>
</body>
</html>